import React, { useEffect, useState } from "react";
import {
	StyleSheet,
	Text,
	View,
	FlatList,
	Image,
	Pressable,
	Dimensions,
	ActivityIndicator,
	TouchableOpacity,
} from "react-native";
import {
	kenticoAPIClient,
	setAuthToken,
} from "../../utils/kenticoInstance";
import { Video } from "../../utils/apis/generated/kentico";
import { AUTH_TOKEN } from "../../config/auth";
// Import navigation hooks and types
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../app/index";
import { scale } from "../../utils/helpers/dimensionScale.helper";

// Screen dimensions and layout constants
const { width } = Dimensions.get("window");
const _spacing = scale(32);
const _item_width = width - 2 * _spacing;
const PLACEHOLDER_IMAGE = require("../../assets/images/placeholder.jpg");

// Type definition for video items
interface VideoItem {
	itemId: string; // Unique identifier for the video
	itemType: string; // Type of the item, e.g., video
	name: string; // Video title
	description: string; // Video description
	duration: string; // Video duration
	poster: string; // URL for the video thumbnail
	fullDescription: string | null; // Full description of the video
	urlSlug: string | null; // URL slug for the video
}

// Navigation type definition
type VideoPlayerNavigationProp = NativeStackNavigationProp<
	RootStackParamList,
	"VideoPlayer" | "VideoDetailsPage"
> & {
	replace(name: keyof RootStackParamList, params?: any): void;
};

interface RelatedVideosProps {
	videoId: string;
	isUpcomingEvent?: boolean;
}

const RelatedVideos = ({
	videoId,
	isUpcomingEvent = false,
}: RelatedVideosProps) => {
	const [relatedVideos, setRelatedVideos] = useState<VideoItem[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	// Get navigation object
	const navigation = useNavigation<VideoPlayerNavigationProp>();

	useEffect(() => {
		console.log(
			"[RelatedVideos] useEffect mounted with videoId:",
			videoId,
			isUpcomingEvent ? "(upcoming event)" : ""
		);

		// If no videoId is provided, don't show an error, just return early
		if (!videoId) {
			console.log(
				"[RelatedVideos] No videoId provided, skipping related videos fetch"
			);
			setIsLoading(false);
			return;
		}

		// Skip API call for upcoming events
		if (isUpcomingEvent) {
			setIsLoading(false);
			return;
		}

		setAuthToken(AUTH_TOKEN);

		/**
		 * Fetches related videos data from Kentico CMS
		 */
		const fetchRelatedVideos = async (videoId: string) => {
			setIsLoading(true);
			setError(null); // Reset error state on new fetch
			try {
				const response = await kenticoAPIClient.ott.getVideoById(
					videoId,
					{ language: "en" }
				);

				if (!response?.data) {
					console.error(
						"[RelatedVideos] Invalid response format - no data"
					);
					// setError("Failed to load related videos");
					return;
				}

				const relatedVideosData =
					response.data.relatedVideos?.items || [];

				console.log(
					"[RelatedVideos] Found",
					relatedVideosData.length,
					"related videos"
				);

				// Transform and validate related video items
				const validVideos = relatedVideosData.map((item: any) => ({
					itemId: item.itemId,
					itemType: item.itemType || "video",
					name: item.name || "Untitled Video",
					description: item.description || "",
					duration: item.duration || "00:00",
					poster: item.poster || "",
					fullDescription: item.fullDescription || null,
					urlSlug: item.urlSlug || null,
				}));

				setRelatedVideos(validVideos);
			} catch (error: any) {
				// Log error but don't display it in UI
				console.log(
					"[RelatedVideos] Error fetching related videos:",
					{
						errorType: error.name,
						errorMessage: error.message,
					}
				);
				// Don't set error state to avoid UI disruption
				setRelatedVideos([]); // Set empty array to show "No related videos"
			} finally {
				setIsLoading(false);
			}
		};

		fetchRelatedVideos(videoId);
	}, [videoId, isUpcomingEvent]);

	/**
	 * Renders individual related video item
	 * Shows video thumbnail and title in a horizontal layout
	 */
	const RelatedItem = ({ item }: { item: VideoItem }) => {
		const [isFocused, setIsFocused] = useState(false);

		return (
			<View style={styles.videoItemContainer}>
				<TouchableOpacity
					style={[styles.relatedItem, isFocused && styles.focused]}
					activeOpacity={1}
					onFocus={() => setIsFocused(true)}
					onBlur={() => setIsFocused(false)}
					onPress={() => {
						// console.log("Pressed video:", item.itemId);
						// Navigate to video details page first
						navigation.navigate("VideoDetailsPage", {
							video: {
								videoId: item.itemId,
								title: item.name,
								thumbnail: item.poster || "",
								description: item.description || "",
								isLive: false,
							},
						});
					}}
					// accessibility attributes
					accessible={true}
					accessibilityRole="button"
					accessibilityLabel={`Play video: ${item.name}`}
				>
					<View style={styles.imageContainer}>
						<Image
							source={
								item.poster && item.poster !== "undefined"
									? { uri: item.poster }
									: PLACEHOLDER_IMAGE
							}
							style={styles.videoImage}
							resizeMode="cover"
							defaultSource={PLACEHOLDER_IMAGE}
						/>
						<View style={styles.durationContainer}>
							<Text style={styles.duration}>{item.duration}</Text>
						</View>
					</View>
				</TouchableOpacity>
				<Text
					style={styles.videoTitle}
					numberOfLines={2}
				>
					{item.name}
				</Text>
			</View>
		);
	};

	if (isLoading) {
		return (
			<View style={[styles.container, { justifyContent: "center" }]}>
				<Text
					style={[
						styles.text,
						{
							fontSize: scale(29),
							marginBottom: scale(42),
						},
					]}
				>
					Loading related videos...
				</Text>
				<ActivityIndicator
					size="large"
					color="#fff"
				/>
			</View>
		);
	}

	// If there's an error or no videoId, don't show anything
	if (error || !videoId) {
		// Just return null to render nothing
		return null;
	}

	if (!relatedVideos.length) {
		return (
			<View style={styles.container}>
				{/* <Text style={styles.text}>No related videos available</Text> */}
				<Text style={[styles.text, { textAlign: "left" }]}>
					No related videos
				</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<Text style={styles.sectionTitle}>Related Videos</Text>
			<FlatList
				data={relatedVideos}
				keyExtractor={(item) => item.itemId}
				renderItem={({ item }) => <RelatedItem item={item} />}
				contentContainerStyle={styles.listContainer}
				horizontal
				showsHorizontalScrollIndicator={false}
				ItemSeparatorComponent={() => (
					<View style={styles.separator} />
				)}
			/>
		</View>
	);
};

export default RelatedVideos;

const styles = StyleSheet.create({
	container: {
		flex: 1,
		width: "100%",
		minHeight: scale(264),
	},
	sectionTitle: {
		color: "#fff",
		fontSize: scale(38),
		fontWeight: "bold",
		marginBottom: 8,
		paddingHorizontal: 16,
		marginTop: 0,
	},
	listContainer: {
		paddingBottom: _spacing,
	},
	separator: {
		height: _spacing / 2,
	},
	relatedItem: {
		width: "100%",
		borderRadius: scale(16),
		overflow: "hidden",
		flexDirection: "row",
		alignItems: "flex-start",
		borderWidth: 0,
		backgroundColor: "#0A192F",
		opacity: 0.7,
	},
	focused: {
		opacity: 1,
	},
	imageContainer: {
		width: scale(288),
		aspectRatio: 16 / 9,
		position: "relative",
		borderRadius: scale(14),
		overflow: "hidden",
	},
	videoImage: {
		width: "100%",
		height: "100%",
	},
	durationContainer: {
		position: "absolute",
		bottom: scale(8),
		right: scale(8),
		backgroundColor: "rgba(0, 0, 0, 0.8)",
		paddingHorizontal: scale(8),
		paddingVertical: scale(4),
		borderRadius: scale(6),
	},
	duration: {
		color: "#fff",
		fontSize: scale(22),
		fontWeight: "500",
	},
	videoDetailsContainer: {
		flex: 1,
		padding: _spacing / 2,
	},
	videoTitle: {
		color: "#fff",
		fontSize: scale(24),
		textAlign: "center",
		marginTop: 4,
		maxWidth: scale(288),
		height: scale(58),
		lineHeight: scale(29),
	},
	text: {
		color: "#fff",
		fontSize: scale(29),
		textAlign: "center",
	},
	videoItemContainer: {
		alignItems: "center",
		marginRight: _spacing / 2,
		flexDirection: "column",
		height: scale(216),
	},
});

/**
{
    "_kenticoCodename": "video_top_10___n_2",
    "_kenticoId": "377bbbe9-903c-4330-b3b4-0f3b806d4aeb",
    "_kenticoLanguage": "en",
    "itemType": "video",

    "relatedVideos": {
        "items": [
            [Object], [Object], [Object], [Object], [Object],
            [Object], [Object], [Object], [Object]
        ]
    },

    "video": {
        "Categories": [[Object], [Object]],
        "ItemProducts": [],
        "Playlist": null,

        "bannerImageName": null,
        "bannerImageUrl": null,
        "bannerRedirectionLink": null,

        "currentFanViews": 0,
        "description": "Kauldi Odriozola prolonge au HBC Nantes jusqu'en 2029",
        "duration": "00:01:14",

        "fullDescription": null,
        "hasBeenViewed": false,
        "isNew": false,

        "itemId": "05b91b8e-d68e-4a3b-b305-e4dfa4528cd7",
        "marker": 0,
        "name": "Kauldi Odriozola prolonge au HBC Nantes jusqu'en 2029",

        "poster": "https://onrewind.imgix.net/thumbnails/4eccc20b-9f1b-8e0d-9d7102d53578/img-15849.jpg",
        "posterPortrait": null,

        "publicationDate": "2024-11-05T14:36:59",
        "ratio": "sixteen-nine",

        "searchTags": [[Object], [Object]],
        "shareUrl": "https://www.handballtv.fr/player/05b91b8e-d68e-4a3b-b305-e4dfa4528cd7/",

        "tags": [[Object], [Object]],
        "technicalDescription": null,
        "urlSlug": null,
        "views": 88
    }
}

{
    "items": [
        {
            "bannerImageName": null,
            "bannerImageUrl": null,
            "bannerRedirectionLink": null,
            "currentFanViews": 0,
            "description": "⏱️ Les dessous d'un reveal maillot surprise, en 24h chrono. 👕 𝙻𝙚 𝙢𝙚𝙞𝙡𝙡𝙚𝙪𝙧 𝙣'𝙖𝙩𝙩𝙚𝙣𝙙 𝙥𝙖𝙨.",
          ! "duration": "00:01:07",
            "fullDescription": null,
            "hasBeenViewed": false,
            "isNew": false,
            "itemId": "cd498352-4464-4d9b-8d41-02ef852799c3",
            "itemType": "video",
            "marker": 0,
          ! "name": "Maillot 2024-2025",
          ! "poster": "https://onrewind.imgix.net/thumbnails/cd498352-4464-4d9b-8d41-02ef852799c3/img-89345.jpg",
            "posterPortrait": null,
            "ratio": "sixteen-nine",
            "technicalDescription": null,
            "urlSlug": null,
            "views": 42
        },
        {
            "bannerImageName": null,
            "bannerImageUrl": null,
            "bannerRedirectionLink": null,
            "currentFanViews": 0,
            "description": "Alexandre Cavalcanti et Jorge Maqueda quittent le HBC Nantes",
            "duration": "00:04:03",
            "fullDescription": null,
            "hasBeenViewed": false,
            "isNew": false,
            "itemId": "6a8e2b6d-8cd6-4036-89cf-a515a2724710",
            "itemType": "video",
            "marker": 0,
            "name": "Merci Alex & Maqué",
            "poster": null,
            "posterPortrait": null,
            "ratio": "sixteen-nine",
            "technicalDescription": null,
            "urlSlug": null,
            "views": 18
        },
        {
            "bannerImageName": null,
            "bannerImageUrl": null,
            "bannerRedirectionLink": null,
            "currentFanViews": 0,
            "description": "Nicolas Tournat est de retour à la maison",
            "duration": "00:00:45",
            "fullDescription": null,
            "hasBeenViewed": false,
            "isNew": false,
            "itemId": "761a7dd5-4cc9-4756-aae7-0b05cc36c4fd",
            "itemType": "video",
            "marker": 0,
            "name": "Nicolas Tournat de retour au HBC Nantes",
            "poster": "https://onrewind.imgix.net/thumbnails/761a7dd5-4cc9-4756-aae7-0b05cc36c4fd/img-67599.jpg",
            "posterPortrait": null,
            "ratio": "sixteen-nine",
            "technicalDescription": null,
            "urlSlug": null,
            "views": 53
        }
    ]
}
 */
