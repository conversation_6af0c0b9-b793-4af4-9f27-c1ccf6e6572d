import React, { useState, useEffect, useCallback } from "react";
import {
	StyleSheet,
	Text,
	View,
	FlatList,
	Image,
	TouchableOpacity,
	ActivityIndicator,
	ImageStyle,
	StyleProp,
} from "react-native";
import { kenticoAPIClient } from "../utils/kenticoInstance";
import {
	VideoCard,
	CategoryCardWithRelationships,
	Cursor,
} from "../utils/apis/generated/kentico";
import {
	useNavigation,
	useRoute,
	RouteProp,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app";
import {
	optimizeImageUrl,
	PRESETS,
} from "../utils/helpers/imageOptimization.helper";
import { scale } from "../utils/helpers/dimensionScale.helper";
import { formatDuration } from "../utils/helpers/formatDuration.helper";

// Define placeholder image at module level
const PLACEHOLDER_IMAGE = require("../assets/images/placeholder.jpg");

type CategoriesPageRouteProp = RouteProp<
	RootStackParamList,
	"Categories"
>;
type CategoriesPageNavigationProp =
	NativeStackNavigationProp<RootStackParamList>;

const CategoriesPage = () => {
	const navigation = useNavigation<CategoriesPageNavigationProp>();
	const route = useRoute<CategoriesPageRouteProp>();
	const flatListRef = React.useRef<FlatList>(null);

	// Extract category ID from route params only
	const categoryId = route.params?.categoryId;
	const categoryName = route.params?.categoryName;

	// State management
	const [categoryDetails, setCategoryDetails] =
		useState<CategoryCardWithRelationships | null>(null);
	const [videos, setVideos] = useState<VideoCard[]>([]);
	const [loading, setLoading] = useState(true);
	const [loadingMore, setLoadingMore] = useState(false);
	const [cursor, setCursor] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Fetch category details - contains basic info
	const fetchCategoryDetails = useCallback(async () => {
		if (!categoryId) {
			setError("No category ID provided");
			setLoading(false);
			return;
		}

		try {
			console.log(
				`[CategoriesPage] Fetching category details for ID: ${categoryId}`
			);
			const response = await kenticoAPIClient.ott.getCategoryByIdV2(
				categoryId,
				{
					language: "en",
				}
			);

			setCategoryDetails(response.data);
			console.log(
				`[CategoriesPage] Successfully fetched category: ${response.data.name}`
			);
		} catch (err) {
			console.error(
				"[CategoriesPage] Error fetching category details:",
				err
			);
			setError("Failed to load category details");
		}
	}, [categoryId]);

	// Fetch initial videos for the category
	const fetchInitialVideos = useCallback(async () => {
		if (loadingMore || !categoryId || videos.length > 0) {
			// Skip if already have videos or currently loading more
			console.log(
				"[CategoriesPage] Skipping initial video fetch - conditions not met"
			);
			return;
		}

		setLoading(true);
		try {
			console.log(
				`[CategoriesPage] Fetching initial videos for category: ${categoryId}`
			);
			const response = await kenticoAPIClient.ott.getCategoryVideosV2(
				categoryId,
				{
					language: "en",
					limit: 20, // Initial page size
				}
			);

			// Handle different response formats
			if ("items" in response.data) {
				const initialItems = response.data.items;
				setVideos(initialItems);
				console.log(
					`[CategoriesPage] Loaded ${initialItems.length} videos initially`
				);

				// Set pagination cursor
				const initialCursor = response.data.cursor?.after || null;
				setCursor(initialCursor);
				setHasMore(!!initialCursor);
			} else if (Array.isArray(response.data)) {
				setVideos(response.data);
				console.log(
					`[CategoriesPage] Loaded ${response.data.length} videos (array format)`
				);
				setHasMore(false); // No cursor in this format
			}
		} catch (err) {
			console.error(
				"[CategoriesPage] Error loading initial videos:",
				err
			);
			setError("Failed to load videos");
		} finally {
			setLoading(false);
		}
	}, [categoryId, loadingMore, videos.length]);

	// Load more videos when reaching end of list
	const handleLoadMore = useCallback(async () => {
		if (loadingMore || !hasMore || !cursor || !categoryId) {
			console.log(
				"[CategoriesPage] Skipping load more - conditions not met"
			);
			return;
		}

		console.log(
			`[CategoriesPage] Loading more videos with cursor: ${cursor}`
		);
		setLoadingMore(true);

		try {
			const response = await kenticoAPIClient.ott.getCategoryVideosV2(
				categoryId,
				{
					language: "en",
					limit: 20,
					after: cursor,
				}
			);

			if ("items" in response.data) {
				const newItems = response.data.items || [];

				if (newItems.length > 0) {
					// Clone previous videos array to avoid reference issues
					const uniqueVideoIds = new Set(
						[...videos].map((v) => v.itemId)
					);
					const uniqueNewItems = newItems.filter(
						(item) => !uniqueVideoIds.has(item.itemId)
					);

					if (uniqueNewItems.length > 0) {
						console.log(
							`[CategoriesPage] Adding ${uniqueNewItems.length} unique new videos`
						);
						setVideos((prevVideos) => [
							...prevVideos,
							...uniqueNewItems,
						]);
					} else {
						console.log(
							"[CategoriesPage] No unique new videos to add"
						);
					}
				} else {
					console.log("[CategoriesPage] No new videos received");
					setHasMore(false);
				}

				// Update cursor for next page
				const newCursor = response.data.cursor?.after || null;
				setCursor(newCursor);
				setHasMore(!!newCursor);
			}
		} catch (err) {
			console.error(
				"[CategoriesPage] Error loading more videos:",
				err
			);
			setError("Failed to load more videos");
		} finally {
			setLoadingMore(false);
		}
	}, [categoryId, cursor, hasMore, loadingMore, videos]);

	// Initial data loading
	useEffect(() => {
		if (categoryId) {
			// Only fetch category details if not already loaded or if we have a new category
			const shouldFetchDetails =
				!categoryDetails || categoryDetails.id !== categoryId;

			// Only fetch videos if we don't have any yet
			const shouldFetchVideos = videos.length === 0;

			console.log(
				`[CategoriesPage] Initial load - shouldFetchDetails: ${shouldFetchDetails}, shouldFetchVideos: ${shouldFetchVideos}`
			);

			const fetchPromises: Promise<void>[] = [];

			if (shouldFetchDetails) {
				fetchPromises.push(fetchCategoryDetails());
			}

			if (shouldFetchVideos) {
				fetchPromises.push(fetchInitialVideos());
			}

			if (fetchPromises.length > 0) {
				Promise.all(fetchPromises);
			} else {
				// Nothing to fetch, ensure loading is set to false
				setLoading(false);
			}
		} else {
			setLoading(false);
			setError("No category ID provided");
		}
	}, [
		fetchCategoryDetails,
		fetchInitialVideos,
		categoryId,
		categoryDetails,
		videos.length,
	]);

	// Define a type for the onEndReached function with the lastCall property
	interface EndReachedFunction {
		(): void;
		lastCall: number;
	}

	const onEndReached = useCallback(() => {
		// Prevent loading if we're already loading or at initial load
		if (videos.length === 0 || loadingMore || loading) return;

		// Add debounce mechanism to prevent multiple calls
		const now = Date.now();
		if (
			(onEndReached as EndReachedFunction).lastCall &&
			now - (onEndReached as EndReachedFunction).lastCall < 1000
		) {
			console.log("[CategoriesPage] Debouncing onEndReached call");
			return;
		}

		// Store last call timestamp
		(onEndReached as EndReachedFunction).lastCall = now;

		console.log("[CategoriesPage] End reached, loading more videos");
		handleLoadMore();
	}, [
		videos.length,
		loadingMore,
		loading,
		handleLoadMore,
	]) as EndReachedFunction;

	// Initialize lastCall property
	(onEndReached as EndReachedFunction).lastCall = 0;

	// Navigate to video details page
	const handleVideoPress = (video: VideoCard) => {
		navigation.navigate("VideoDetailsPage", {
			video: {
				videoId: video.itemId,
				title: video.name,
				thumbnail: video.poster || video.posterPortrait || "",
				description: video.description || "",
				ratio: video.ratio,
			},
		});
	};

	// Image rendering with fallback
	const renderImage = useCallback(
		(
			imageUrl: string | null | undefined,
			style?: StyleProp<ImageStyle>,
			resizeMode: "contain" | "cover" | "stretch" = "stretch"
		) => {
			if (!imageUrl) {
				return (
					<Image
						source={PLACEHOLDER_IMAGE}
						style={[style]}
						resizeMode={resizeMode}
					/>
				);
			}

			try {
				const shouldOptimize = imageUrl.startsWith("http");
				const finalUrl = shouldOptimize
					? optimizeImageUrl(imageUrl, PRESETS.OPTIMIZED)
					: imageUrl;

				return (
					<Image
						source={finalUrl ? { uri: finalUrl } : PLACEHOLDER_IMAGE}
						style={[style]}
						resizeMode={resizeMode}
						onError={(error) => {
							console.error(
								"[CategoriesPage][renderImage] Image load error:",
								error
							);
						}}
					/>
				);
			} catch (error) {
				console.error(
					"[CategoriesPage][renderImage] Error rendering image:",
					error
				);
				return (
					<Image
						source={PLACEHOLDER_IMAGE}
						style={[style]}
						resizeMode={resizeMode}
					/>
				);
			}
		},
		[]
	);

	// Render lock overlay for paid content
	const renderLockOverlay = useCallback(
		() => (
			<View style={styles.lockIconWrapper}>
				<View style={styles.lockIconContainer}>
					<Text style={styles.lockIcon}>🔒</Text>
				</View>
			</View>
		),
		[]
	);

	// Render video item card
	const renderVideoItem = ({
		item,
		index,
	}: {
		item: VideoCard;
		index: number;
	}) => {
		// Check if content is paid based on products existence
		const isPaid = item.products && item.products.length > 0;

		// Check if this item is in the last row when there are less than 4 items remaining
		const remainingItems = videos.length % 4;
		const isInLastIncompleteRow =
			remainingItems !== 0 && index >= videos.length - remainingItems;

		return (
			<TouchableOpacity
				style={[
					styles.videoCard,
					// Apply fixed width to all items in the incomplete last row
					isInLastIncompleteRow && styles.lastVideoCard,
					{ opacity: 0.7 },
				]}
				activeOpacity={1}
				onPress={() => handleVideoPress(item)}
				hasTVPreferredFocus={index === 0}
			>
				<View style={styles.thumbnailContainer}>
					{renderImage(
						item.poster || item.posterPortrait,
						styles.thumbnail
					)}
					{isPaid && renderLockOverlay()}
				</View>
				<View style={styles.videoInfoContainer}>
					<View style={styles.titleRow}>
						<Text
							style={[styles.videoTitle, { flex: 1 }]}
							numberOfLines={2}
						>
							{item.name}
						</Text>
						{item.duration && (
							<Text style={styles.durationText}>
								{formatDuration(item.duration)}
							</Text>
						)}
					</View>
				</View>
			</TouchableOpacity>
		);
	};

	// Render loading, error or content
	if (loading && !videos.length) {
		return (
			<View style={styles.loadingContainer}>
				<ActivityIndicator
					size="large"
					color="#FFFFFF"
				/>
				<Text style={styles.loadingText}>Loading category...</Text>
			</View>
		);
	}

	if (error && !videos.length) {
		return (
			<View style={styles.errorContainer}>
				<Text style={styles.errorText}>{error}</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<FlatList
				ref={flatListRef}
				data={videos}
				renderItem={renderVideoItem}
				keyExtractor={(item) => item.itemId}
				numColumns={4}
				contentContainerStyle={styles.gridContent}
				onEndReached={onEndReached}
				onEndReachedThreshold={0.5}
				showsVerticalScrollIndicator={false}
				removeClippedSubviews={true}
				maxToRenderPerBatch={8}
				initialNumToRender={8}
				windowSize={21}
				maintainVisibleContentPosition={{
					minIndexForVisible: 0,
				}}
				extraData={videos.length}
				ListHeaderComponent={() => (
					<TouchableOpacity
						style={styles.headerBanner}
						activeOpacity={0.9}
						onPress={() => {
							// Scroll to top when pressed
							if (flatListRef.current) {
								flatListRef.current.scrollToOffset({
									offset: 0,
									animated: true,
								});
							}
						}}
					>
						{renderImage(
							route.params?.categoryImage,
							styles.headerImage,
							"cover"
						)}
						<View style={styles.headerOverlay}>
							<Text style={styles.headerTitle}>
								{categoryDetails?.name || categoryName || "Category"}
							</Text>
							{categoryDetails?.description && (
								<Text style={styles.categoryDescription}>
									{categoryDetails.description}
								</Text>
							)}
						</View>
					</TouchableOpacity>
				)}
				ListEmptyComponent={
					<View style={styles.emptyContainer}>
						<Text style={styles.emptyText}>No videos available</Text>
					</View>
				}
				ListFooterComponent={
					loadingMore ? (
						<View style={styles.footerLoader}>
							<ActivityIndicator
								size="small"
								color="#FFFFFF"
							/>
						</View>
					) : null
				}
			/>
		</View>
	);
};

export default CategoriesPage;

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: "#102e55",
	},
	gridContent: {
		paddingHorizontal: 0,
		paddingBottom: scale(100),
	},
	videoCard: {
		flex: 1,
		margin: scale(8),
		borderRadius: 8,
		overflow: "hidden",
		height: scale(360),
	},
	lastVideoCard: {
		flex: 0, // Override flex
		width: scale(460), // Fixed width for last item when alone
	},
	thumbnail: {
		width: "100%",
		height: scale(240),
		borderRadius: 8,
	},
	thumbnailContainer: {
		position: "relative",
		width: "100%",
		height: scale(240),
		borderRadius: 8,
		overflow: "hidden",
		backgroundColor: "#000",
	},
	videoInfoContainer: {
		padding: scale(16),
		paddingBottom: 0,
		height: scale(120),
		overflow: "hidden",
	},
	titleRow: {
		flexDirection: "row",
		alignItems: "flex-start",
		justifyContent: "space-between",
		gap: scale(16),
	},
	videoTitle: {
		color: "#FFFFFF",
		fontSize: scale(32),
		fontWeight: "bold",
		lineHeight: scale(40),
		maxHeight: scale(80),
		overflow: "hidden",
	},
	durationText: {
		color: "#FFFFFF",
		fontSize: scale(24),
		fontWeight: "bold",
		opacity: 0.8,
	},
	headerBanner: {
		width: "100%",
		height: scale(650),
		zIndex: 1,
		marginBottom: scale(30),
	},
	headerImage: {
		width: "100%",
		height: "100%",
		backgroundColor: "#000000",
	},
	headerOverlay: {
		position: "absolute",
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		backgroundColor: "rgba(0, 0, 0, 0.3)",
		padding: scale(60),
		justifyContent: "flex-end",
	},
	headerTitle: {
		color: "#FFFFFF",
		fontSize: scale(64),
		fontWeight: "bold",
		marginBottom: scale(30),
	},
	categoryDescription: {
		color: "#FFFFFF",
		fontSize: scale(32),
		opacity: 0.8,
		marginBottom: scale(60),
	},
	loadingContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#102e55",
	},
	loadingText: {
		color: "#FFFFFF",
		marginTop: scale(40),
		fontSize: scale(36),
	},
	errorContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#102e55",
		padding: scale(40),
	},
	errorText: {
		color: "#FFFFFF",
		fontSize: scale(36),
		marginBottom: scale(40),
		textAlign: "center",
	},
	emptyContainer: {
		alignItems: "center",
		justifyContent: "center",
		padding: scale(100),
	},
	emptyText: {
		color: "#FFFFFF",
		fontSize: scale(36),
		opacity: 0.8,
	},
	footerLoader: {
		paddingVertical: scale(20),
		alignItems: "center",
		marginTop: scale(20),
	},
	// Lock overlay styles for paid content
	lockOverlay: {
		position: "absolute",
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		borderRadius: 8,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "rgba(255, 0, 0, 0.2)",
	},
	lockIconWrapper: {
		position: "absolute",
		bottom: scale(16),
		right: scale(16),
	},
	lockIconContainer: {
		width: scale(48),
		height: scale(48),
		borderRadius: scale(24),
		backgroundColor: "rgba(0, 0, 0, 0.7)",
		justifyContent: "center",
		alignItems: "center",
	},
	lockIcon: {
		fontSize: scale(28),
	},
});
