import React, {
	createContext,
	useContext,
	useState,
	useEffect,
	ReactNode,
} from "react";

// Conditional import for AsyncStorage with fallback
let AsyncStorage: any = null;
try {
	AsyncStorage =
		require("@react-native-async-storage/async-storage").default;
} catch (error) {
	console.warn(
		"AsyncStorage not available, language preference will not persist across app restarts"
	);
}

// Storage key for persisting language preference
const LANGUAGE_STORAGE_KEY = "app_language";

// Supported languages
export type SupportedLanguage = "en" | "fr";

// Language context interface
interface LanguageContextType {
	currentLanguage: SupportedLanguage;
	setLanguage: (language: SupportedLanguage) => Promise<void>;
	isLoading: boolean;
}

// Create the context
const LanguageContext = createContext<
	LanguageContextType | undefined
>(undefined);

// Language provider props
interface LanguageProviderProps {
	children: ReactNode;
}

/**
 * Language Provider Component
 * Manages global language state with AsyncStorage persistence
 *
 * Features:
 * - Persists language preference across app restarts
 * - Provides global language state to all components
 * - Handles loading state during initialization
 */
export const LanguageProvider: React.FC<LanguageProviderProps> = ({
	children,
}) => {
	const [currentLanguage, setCurrentLanguage] =
		useState<SupportedLanguage>("en");
	const [isLoading, setIsLoading] = useState(true);

	/**
	 * Load saved language preference from AsyncStorage on app start
	 */
	useEffect(() => {
		const loadSavedLanguage = async () => {
			try {
				if (AsyncStorage) {
					const savedLanguage = await AsyncStorage.getItem(
						LANGUAGE_STORAGE_KEY
					);
					if (
						savedLanguage &&
						(savedLanguage === "en" || savedLanguage === "fr")
					) {
						setCurrentLanguage(savedLanguage as SupportedLanguage);
						console.log(`Loaded saved language: ${savedLanguage}`);
					} else {
						console.log("No saved language found, using default: en");
					}
				} else {
					console.log(
						"AsyncStorage not available, using default language: en"
					);
				}
			} catch (error) {
				console.error("Error loading saved language:", error);
				// Keep default language (en) if loading fails
			} finally {
				setIsLoading(false);
			}
		};

		loadSavedLanguage();
	}, []);

	/**
	 * Set new language and persist to AsyncStorage
	 * @param language - The language to set
	 */
	const setLanguage = async (language: SupportedLanguage) => {
		try {
			// Update state immediately for responsive UI
			setCurrentLanguage(language);

			// Persist to AsyncStorage if available
			if (AsyncStorage) {
				await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
				console.log(
					`Language changed to: ${language} and saved to storage`
				);
			} else {
				console.log(
					`Language changed to: ${language} (not persisted - AsyncStorage unavailable)`
				);
			}
		} catch (error) {
			console.error("Error saving language preference:", error);
			// Language change still works in current session even if saving fails
		}
	};

	const contextValue: LanguageContextType = {
		currentLanguage,
		setLanguage,
		isLoading,
	};

	return (
		<LanguageContext.Provider value={contextValue}>
			{children}
		</LanguageContext.Provider>
	);
};

/**
 * Custom hook to use the Language Context
 * @returns Language context value
 * @throws Error if used outside of LanguageProvider
 */
export const useLanguage = (): LanguageContextType => {
	const context = useContext(LanguageContext);
	if (context === undefined) {
		throw new Error(
			"useLanguage must be used within a LanguageProvider"
		);
	}
	return context;
};
